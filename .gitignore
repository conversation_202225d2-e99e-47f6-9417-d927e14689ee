# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
/.pnp
.pnp.js

# Testing
/coverage
.nyc_output

# Production
/build
dist
dist-ssr
*.local

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Misc
.cache
.temp
.tmp
*.bak
.sass-cache/
.eslintcache
.stylelintcache

# Package managers
yarn.lock
package-lock.json
.npm
