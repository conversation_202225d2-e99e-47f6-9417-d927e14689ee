import React from 'react'
import { I<PERSON><PERSON><PERSON>on, Tooltip } from '@mui/material'
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
} from '@mui/icons-material'
import { useThemeMode } from '@/hooks/useThemeMode'

const ThemeToggle = (): React.JSX.Element => {
  const { mode, toggleMode } = useThemeMode()

  return (
    <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
      <IconButton
        onClick={toggleMode}
        color='inherit'
        sx={{
          transition: 'transform 0.3s ease-in-out',
          '&:hover': {
            transform: 'rotate(180deg)',
          },
        }}
      >
        {mode === 'light' ? (
          <DarkModeIcon />
        ) : (
          <LightModeIcon sx={{ color: '#ffa618' }} />
        )}
      </IconButton>
    </Tooltip>
  )
}

export default ThemeToggle
