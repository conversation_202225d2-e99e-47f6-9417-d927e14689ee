import React from 'react'
import {
  Menu,
  MenuItem,
  Box,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Business as BusinessIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const WorkspacesMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 280,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Workspaces
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <PersonIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Personal Workspace</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <GroupIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Team Workspace</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <BusinessIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Company Workspace</ListItemText>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <AddIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Create Workspace</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <SettingsIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Workspace Settings</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default WorkspacesMenu
