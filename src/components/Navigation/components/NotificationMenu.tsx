import React from 'react'
import { Menu, MenuItem, Box, Typography, Divider } from '@mui/material'
import { MenuProps } from '../types'

const NotificationMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 350,
            maxWidth: 400,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Notifications
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <Box sx={{ width: '100%' }}>
          <Typography variant='subtitle2' sx={{ fontWeight: 600 }}>
            New card added
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 0.5 }}>
            "Implement authentication" was added to In Progress
          </Typography>
          <Typography variant='caption' color='text.secondary' sx={{ mt: 0.5 }}>
            2 minutes ago
          </Typography>
        </Box>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <Box sx={{ width: '100%' }}>
          <Typography variant='subtitle2' sx={{ fontWeight: 600 }}>
            Card moved
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 0.5 }}>
            "Project setup" was moved to Done
          </Typography>
          <Typography variant='caption' color='text.secondary' sx={{ mt: 0.5 }}>
            1 hour ago
          </Typography>
        </Box>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <Box sx={{ width: '100%' }}>
          <Typography variant='subtitle2' sx={{ fontWeight: 600 }}>
            Due date reminder
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 0.5 }}>
            "Design homepage" is due tomorrow
          </Typography>
          <Typography variant='caption' color='text.secondary' sx={{ mt: 0.5 }}>
            3 hours ago
          </Typography>
        </Box>
      </MenuItem>
    </Menu>
  )
}

export default NotificationMenu
