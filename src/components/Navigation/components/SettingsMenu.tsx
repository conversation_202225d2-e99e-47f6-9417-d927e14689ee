import React from 'react'
import {
  Menu,
  MenuItem,
  Box,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  Palette as PaletteIcon,
  Language as LanguageIcon,
  Security as SecurityIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const SettingsMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 250,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Settings
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <PaletteIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Appearance</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <LanguageIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Language</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <SecurityIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Privacy & Security</ListItemText>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <SettingsIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>General Settings</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default SettingsMenu
