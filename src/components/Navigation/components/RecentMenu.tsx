import React from 'react'
import {
  Menu,
  MenuItem,
  Box,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  ViewKanban as ViewKanbanIcon,
  AccessTime as AccessTimeIcon,
  ClearAll as ClearAllIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const RecentMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 320,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Recently Viewed
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <DashboardIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Project Management Board'
          secondary='Team Workspace • 2 hours ago'
        />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <ViewKanbanIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Sprint Planning'
          secondary='Personal Workspace • 1 day ago'
        />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <DashboardIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Marketing Campaign'
          secondary='Company Workspace • 3 days ago'
        />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <ViewKanbanIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Bug Tracking'
          secondary='Team Workspace • 1 week ago'
        />
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <ClearAllIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Clear Recent History</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default RecentMenu
