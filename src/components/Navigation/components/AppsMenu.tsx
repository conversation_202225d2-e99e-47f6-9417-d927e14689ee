import React from 'react'
import {
  Menu,
  MenuItem,
  Box,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  ViewKanban as ViewKanbanIcon,
  Group as GroupIcon,
  Business as BusinessIcon,
  Home as HomeIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const AppsMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 280,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Apps & Workspaces
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <HomeIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Home Dashboard</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <DashboardIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>My Boards</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <ViewKanbanIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Templates</ListItemText>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <GroupIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Team Workspace</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <BusinessIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Company Workspace</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default AppsMenu
