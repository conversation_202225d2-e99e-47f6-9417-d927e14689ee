import React from 'react'
import {
  Menu,
  MenuItem,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  AccountCircle as AccountCircleIcon,
  Logout as LogoutIcon,
  Help as HelpIcon,
  Feedback as FeedbackIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const ProfileMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 220,
            borderRadius: 2,
          },
        },
      }}
    >
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <AccountCircleIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Profile & Visibility</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <SettingsIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Account Settings</ListItemText>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <HelpIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Help</ListItemText>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <FeedbackIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Send Feedback</ListItemText>
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <LogoutIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Logout</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default ProfileMenu
