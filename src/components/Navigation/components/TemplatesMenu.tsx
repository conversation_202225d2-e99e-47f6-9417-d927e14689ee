import React from 'react'
import {
  Menu,
  MenuItem,
  Box,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material'
import {
  ViewKanban as ViewKanbanIcon,
  Assignment as AssignmentIcon,
  Timeline as TimelineIcon,
  Group as GroupIcon,
  Business as BusinessIcon,
  Add as AddIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const TemplatesMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 320,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Board Templates
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <ViewKanbanIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Kanban Template'
          secondary='Basic kanban board for task management'
        />
        <Chip label='Popular' size='small' color='primary' />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <AssignmentIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Project Management'
          secondary='Complete project tracking template'
        />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <TimelineIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Sprint Planning'
          secondary='Agile sprint planning and tracking'
        />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <GroupIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Team Collaboration'
          secondary='Team workflow and communication'
        />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <BusinessIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Business Process'
          secondary='Business workflow management'
        />
      </MenuItem>
      <Divider />
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <AddIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText>Create Custom Template</ListItemText>
      </MenuItem>
    </Menu>
  )
}

export default TemplatesMenu
