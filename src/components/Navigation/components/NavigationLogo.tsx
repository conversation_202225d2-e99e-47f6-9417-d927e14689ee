import React from 'react'
import { Box, Typography, SvgIcon } from '@mui/material'
import TrelloIcon from '@/assets/icons/TrelloIcon.svg?react'

const NavigationLogo = (): React.JSX.Element => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <SvgIcon component={TrelloIcon} inheritViewBox fontSize='small' />
      <Typography
        variant='h4'
        component='h4'
        sx={{ ml: 1, fontSize: '16px', display: { xs: 'none', md: 'block' } }}
      >
        Trello
      </Typography>
    </Box>
  )
}

export default NavigationLogo
