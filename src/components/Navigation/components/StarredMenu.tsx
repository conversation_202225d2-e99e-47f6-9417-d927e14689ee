import React from 'react'
import {
  Menu,
  MenuItem,
  Box,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Star as StarIcon,
  Dashboard as DashboardIcon,
  ViewKanban as ViewKanbanIcon,
  StarBorder as StarBorderIcon,
} from '@mui/icons-material'
import { MenuProps } from '../types'

const StarredMenu = ({
  anchorEl,
  open,
  onClose,
}: MenuProps): React.JSX.Element => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      slotProps={{
        paper: {
          elevation: 3,
          sx: {
            mt: 1.5,
            minWidth: 300,
            borderRadius: 2,
          },
        },
      }}
    >
      <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant='h6' component='div'>
          Starred Boards
        </Typography>
      </Box>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <DashboardIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Project Management Board'
          secondary='Team Workspace'
        />
        <StarIcon fontSize='small' color='warning' />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <ViewKanbanIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText primary='Personal Tasks' secondary='Personal Workspace' />
        <StarIcon fontSize='small' color='warning' />
      </MenuItem>
      <MenuItem onClick={onClose}>
        <ListItemIcon>
          <DashboardIcon fontSize='small' />
        </ListItemIcon>
        <ListItemText
          primary='Marketing Strategy'
          secondary='Company Workspace'
        />
        <StarIcon fontSize='small' color='warning' />
      </MenuItem>
      <Divider />
      <Box sx={{ px: 2, py: 1 }}>
        <Typography variant='body2' color='text.secondary'>
          Click the star icon on any board to add it to your starred list
        </Typography>
      </Box>
    </Menu>
  )
}

export default StarredMenu
