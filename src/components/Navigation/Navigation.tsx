import React, { useState } from 'react'
import {
  AppBar,
  IconButton,
  Avatar,
  Box,
  Tooltip,
  Badge,
  Button,
  TextField,
} from '@mui/material'
import {
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  HelpOutline as HelpOutlineIcon,
  Add,
} from '@mui/icons-material'
import ThemeToggle from '../ThemeToggle/ThemeToggle'
import AppsIcon from '@mui/icons-material/Apps'
import NavigationLogo from './components/NavigationLogo'
import ProfileMenu from './components/ProfileMenu'
import NotificationMenu from './components/NotificationMenu'
import AppsMenu from './components/AppsMenu'
import SettingsMenu from './components/SettingsMenu'
import WorkspacesMenu from './components/WorkspacesMenu'
import RecentMenu from './components/RecentMenu'
import StarredMenu from './components/StarredMenu'
import TemplatesMenu from './components/TemplatesMenu'

const Navigation = (): React.JSX.Element => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)
  const [notificationAnchor, setNotificationAnchor] =
    useState<HTMLElement | null>(null)
  const [appsAnchor, setAppsAnchor] = useState<HTMLElement | null>(null)
  const [settingsAnchor, setSettingsAnchor] = useState<HTMLElement | null>(null)
  const [workspacesAnchor, setWorkspacesAnchor] = useState<HTMLElement | null>(
    null
  )
  const [recentAnchor, setRecentAnchor] = useState<HTMLElement | null>(null)
  const [starredAnchor, setStarredAnchor] = useState<HTMLElement | null>(null)
  const [templatesAnchor, setTemplatesAnchor] = useState<HTMLElement | null>(
    null
  )

  const handleProfileMenuOpen = (
    event: React.MouseEvent<HTMLElement>
  ): void => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = (): void => {
    setAnchorEl(null)
  }

  const handleNotificationMenuOpen = (
    event: React.MouseEvent<HTMLElement>
  ): void => {
    setNotificationAnchor(event.currentTarget)
  }

  const handleNotificationMenuClose = (): void => {
    setNotificationAnchor(null)
  }

  const handleAppsMenuClose = (): void => {
    setAppsAnchor(null)
  }

  const handleSettingsMenuOpen = (
    event: React.MouseEvent<HTMLElement>
  ): void => {
    setSettingsAnchor(event.currentTarget)
  }

  const handleSettingsMenuClose = (): void => {
    setSettingsAnchor(null)
  }

  const handleWorkspacesMenuOpen = (
    event: React.MouseEvent<HTMLElement>
  ): void => {
    setWorkspacesAnchor(event.currentTarget)
  }

  const handleWorkspacesMenuClose = (): void => {
    setWorkspacesAnchor(null)
  }

  const handleRecentMenuOpen = (event: React.MouseEvent<HTMLElement>): void => {
    setRecentAnchor(event.currentTarget)
  }

  const handleRecentMenuClose = (): void => {
    setRecentAnchor(null)
  }

  const handleStarredMenuOpen = (
    event: React.MouseEvent<HTMLElement>
  ): void => {
    setStarredAnchor(event.currentTarget)
  }

  const handleStarredMenuClose = (): void => {
    setStarredAnchor(null)
  }

  const handleTemplatesMenuOpen = (
    event: React.MouseEvent<HTMLElement>
  ): void => {
    setTemplatesAnchor(event.currentTarget)
  }

  const handleTemplatesMenuClose = (): void => {
    setTemplatesAnchor(null)
  }

  const isMenuOpen = Boolean(anchorEl)
  const isNotificationOpen = Boolean(notificationAnchor)
  const isAppsOpen = Boolean(appsAnchor)
  const isSettingsOpen = Boolean(settingsAnchor)
  const isWorkspacesOpen = Boolean(workspacesAnchor)
  const isRecentOpen = Boolean(recentAnchor)
  const isStarredOpen = Boolean(starredAnchor)
  const isTemplatesOpen = Boolean(templatesAnchor)

  return (
    <AppBar position='static' elevation={1}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: theme => theme.layout?.navHeight,
          backgroundColor: 'background.default',
        }}
        px={2}
        gap={2}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 1,
            color: 'primary.main',
          }}
        >
          <AppsIcon fontSize='medium' />
          <NavigationLogo />

          <Box sx={{ display: { xs: 'none', md: 'block' } }}>
            <Button
              color='inherit'
              endIcon={<ExpandMoreIcon />}
              onClick={handleWorkspacesMenuOpen}
              sx={{ textTransform: 'none', minWidth: 'auto' }}
            >
              Workspaces
            </Button>

            <Button
              color='inherit'
              endIcon={<ExpandMoreIcon />}
              onClick={handleRecentMenuOpen}
              sx={{ textTransform: 'none', minWidth: 'auto' }}
            >
              Recent
            </Button>

            <Button
              color='inherit'
              endIcon={<ExpandMoreIcon />}
              onClick={handleStarredMenuOpen}
              sx={{ textTransform: 'none', minWidth: 'auto' }}
            >
              Starred
            </Button>

            <Button
              color='inherit'
              endIcon={<ExpandMoreIcon />}
              onClick={handleTemplatesMenuOpen}
              sx={{ textTransform: 'none', minWidth: 'auto' }}
            >
              Templates
            </Button>
          </Box>

          <Button
            variant='contained'
            sx={{ width: { xs: '40px', lg: 'auto' }, minWidth: { xs: 'auto' } }}
          >
            <Box component='span' sx={{ display: { xs: 'none', lg: 'block' } }}>
              Create
            </Box>
            <Add />
          </Button>
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            color: 'primary.main',
          }}
        >
          <TextField
            label='Search...'
            type='search'
            size='small'
            variant='outlined'
            sx={{ width: 150 }}
          />

          <Tooltip title='Notifications'>
            <IconButton color='inherit' onClick={handleNotificationMenuOpen}>
              <Badge badgeContent={3} color='error' variant='dot'>
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          <ThemeToggle />

          <Tooltip title='Settings'>
            <IconButton color='inherit' onClick={handleSettingsMenuOpen}>
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title='Help'>
            <HelpOutlineIcon sx={{ cursor: 'pointer' }} />
          </Tooltip>

          <Tooltip title='Profile'>
            <IconButton onClick={handleProfileMenuOpen} color='inherit'>
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
                JD
              </Avatar>
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* dropdown Menus */}
      <ProfileMenu
        anchorEl={anchorEl}
        open={isMenuOpen}
        onClose={handleProfileMenuClose}
      />

      <NotificationMenu
        anchorEl={notificationAnchor}
        open={isNotificationOpen}
        onClose={handleNotificationMenuClose}
      />

      <AppsMenu
        anchorEl={appsAnchor}
        open={isAppsOpen}
        onClose={handleAppsMenuClose}
      />

      <SettingsMenu
        anchorEl={settingsAnchor}
        open={isSettingsOpen}
        onClose={handleSettingsMenuClose}
      />

      <WorkspacesMenu
        anchorEl={workspacesAnchor}
        open={isWorkspacesOpen}
        onClose={handleWorkspacesMenuClose}
      />

      <RecentMenu
        anchorEl={recentAnchor}
        open={isRecentOpen}
        onClose={handleRecentMenuClose}
      />

      <StarredMenu
        anchorEl={starredAnchor}
        open={isStarredOpen}
        onClose={handleStarredMenuClose}
      />

      <TemplatesMenu
        anchorEl={templatesAnchor}
        open={isTemplatesOpen}
        onClose={handleTemplatesMenuClose}
      />
    </AppBar>
  )
}

export default Navigation
