import { Box, Container } from '@mui/material'
import Navigation from './components/Navigation'
import BoardBar from './components/BoardBar/BoardBar'

type ViewType = 'home' | 'board' | 'typography' | 'theme' | 'aliases'

const App = () => {
  return (
    <Container disableGutters maxWidth={false} sx={{ height: '100vh' }}>
      <Navigation />
      <BoardBar />
      <Box
        sx={{
          height: theme =>
            `calc(100vh - (${theme.layout?.navHeight} + ${theme.layout?.boardBarHeight}))`,
        }}
      ></Box>
    </Container>
  )
}

export default App
